package models

import "time"

// SocialLink represents a social media or custom link
type SocialLink struct {
	Platform string `json:"platform" bson:"platform"` // e.g., "twitter", "github", "linkedin", "custom"
	URL      string `json:"url" bson:"url"`
	Label    string `json:"label,omitempty" bson:"label,omitempty"` // For custom links
}

// UserProfile represents user's public profile information (separate collection)
type UserProfile struct {
	LoginId     string       `json:"loginId" bson:"loginId"`   // 用于公开查询
	Provider    string       `json:"provider" bson:"provider"` // 用于区分不同provider的同名用户
	CreatedAt   time.Time    `json:"createdAt" bson:"createdAt"`
	UpdatedAt   time.Time    `json:"updatedAt" bson:"updatedAt"`
	DisplayName string       `json:"displayName,omitempty" bson:"displayName,omitempty"` // 公开显示名称
	Avatar      string       `json:"avatar,omitempty" bson:"avatar,omitempty"`           // 头像URL
	Bio         string       `json:"bio" bson:"bio,omitempty"`
	Location    string       `json:"location" bson:"location,omitempty"`
	Website     string       `json:"website" bson:"website,omitempty"`
	SocialLinks []SocialLink `json:"socialLinks" bson:"socialLinks,omitempty"`
	IsPublic    bool         `json:"isPublic" bson:"isPublic"` // 是否公开档案
}

type User struct {
	UUID              string    `json:"uuid" bson:"uuid"`
	CreatedAt         time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt" bson:"updatedAt"`
	Email             string    `json:"email" bson:"email"`
	Image             string    `json:"image" bson:"image"`
	Name              string    `json:"name" bson:"name"`
	Provider          string    `json:"provider" bson:"provider"`
	ProviderAccountId string    `json:"providerAccountId" bson:"providerAccountId"`
	StripeCustomerId  string    `json:"stripeCustomerId,omitempty" bson:"stripeCustomerId,omitempty"`
}

type OriginalUserData interface{}
