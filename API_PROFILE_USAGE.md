# User Profile API 使用说明

## 概述

新增的用户档案API允许用户管理个人资料信息，包括社交媒体链接和自定义链接。用户数据和用户档案分别存储在不同的collection中，档案信息是公开的，可以通过loginId查询。

## 架构设计

- **用户数据 (users collection)**: 存储私密信息如email、stripe信息等
- **用户档案 (profiles collection)**: 存储公开信息如社交链接、简介等
- **公开查询**: 任何人都可以通过loginId和provider查询公开的用户档案

## 接口列表

### 1. 获取用户档案信息

**接口地址：** `GET /api/user/profile`

**请求头：**
- 需要用户认证（通过session）

**响应示例：**
```json
{
  "data": {
    "loginId": "12345",
    "provider": "github",
    "displayName": "张三",
    "avatar": "https://avatars.githubusercontent.com/u/12345",
    "bio": "我是一个开发者",
    "location": "北京",
    "website": "https://example.com",
    "socialLinks": [
      {
        "platform": "github",
        "url": "https://github.com/username"
      },
      {
        "platform": "twitter",
        "url": "https://twitter.com/username"
      },
      {
        "platform": "custom",
        "url": "https://myblog.com",
        "label": "我的博客"
      }
    ],
    "isPublic": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 更新用户档案信息

**接口地址：** `PUT /api/user/profile`

**请求头：**
- `Content-Type: application/json`
- 需要用户认证（通过session）

**请求体示例：**
```json
{
  "displayName": "李四",
  "avatar": "https://example.com/avatar.jpg",
  "bio": "我是一个全栈开发者，专注于Go和React",
  "location": "上海",
  "website": "https://mywebsite.com",
  "socialLinks": [
    {
      "platform": "github",
      "url": "https://github.com/myusername"
    },
    {
      "platform": "twitter",
      "url": "https://twitter.com/myusername"
    },
    {
      "platform": "linkedin",
      "url": "https://linkedin.com/in/myusername"
    },
    {
      "platform": "custom",
      "url": "https://myblog.dev",
      "label": "技术博客"
    }
  ],
  "isPublic": true
}
```

**响应示例：**
```json
{
  "data": {
    "loginId": "12345",
    "provider": "github",
    "displayName": "李四",
    "avatar": "https://example.com/avatar.jpg",
    "bio": "我是一个全栈开发者，专注于Go和React",
    "location": "上海",
    "website": "https://mywebsite.com",
    "socialLinks": [
      {
        "platform": "github",
        "url": "https://github.com/myusername"
      },
      {
        "platform": "twitter",
        "url": "https://twitter.com/myusername"
      },
      {
        "platform": "linkedin",
        "url": "https://linkedin.com/in/myusername"
      },
      {
        "platform": "custom",
        "url": "https://myblog.dev",
        "label": "技术博客"
      }
    ],
    "isPublic": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 查询公开用户档案

**接口地址：** `GET /api/public/profile`

**请求参数：**
- `loginId` (string, 必填): 用户登录ID
- `provider` (string, 必填): OAuth提供商 (github, google, linkedin)

**请求示例：**
```
GET /api/public/profile?loginId=12345&provider=github
```

**响应示例：**
```json
{
  "data": {
    "loginId": "12345",
    "provider": "github",
    "displayName": "张三",
    "avatar": "https://avatars.githubusercontent.com/u/12345",
    "bio": "我是一个开发者",
    "location": "北京",
    "website": "https://example.com",
    "socialLinks": [
      {
        "platform": "github",
        "url": "https://github.com/username"
      },
      {
        "platform": "twitter",
        "url": "https://twitter.com/username"
      }
    ],
    "isPublic": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

## 数据结构说明

### UserProfile
- `loginId` (string, 必填): 用户登录ID，用于公开查询
- `provider` (string, 必填): OAuth提供商 (github, google, linkedin)
- `displayName` (string, 可选): 公开显示名称
- `avatar` (string, 可选): 头像URL
- `bio` (string, 可选): 用户简介
- `location` (string, 可选): 用户位置
- `website` (string, 可选): 用户网站
- `socialLinks` (array, 可选): 社交媒体链接数组
- `isPublic` (boolean, 必填): 是否公开档案
- `createdAt` (string): 创建时间
- `updatedAt` (string): 更新时间

### SocialLink
- `platform` (string, 必填): 平台名称，如 "github", "twitter", "linkedin", "custom"
- `url` (string, 必填): 链接地址
- `label` (string, 可选): 自定义标签，当 platform 为 "custom" 时必填

## 支持的社交平台

常见的社交平台包括：
- `github` - GitHub
- `twitter` - Twitter/X
- `linkedin` - LinkedIn
- `facebook` - Facebook
- `instagram` - Instagram
- `youtube` - YouTube
- `tiktok` - TikTok
- `custom` - 自定义链接（需要提供 label）

## 错误响应

### 400 Bad Request
```json
{
  "error": "Invalid profile data: ..."
}
```

### 401 Unauthorized
```json
{
  "error": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "error": "User not found"
}
```

## 使用示例

### JavaScript/Fetch 示例

```javascript
// 获取用户档案
async function getUserProfile() {
  const response = await fetch('/api/user/profile', {
    credentials: 'include' // 包含session cookie
  });
  const data = await response.json();
  return data;
}

// 更新用户档案
async function updateUserProfile(profileData) {
  const response = await fetch('/api/user/profile', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include',
    body: JSON.stringify(profileData)
  });
  const data = await response.json();
  return data;
}

// 查询公开用户档案
async function getPublicProfile(loginId, provider) {
  const response = await fetch(`/api/public/profile?loginId=${loginId}&provider=${provider}`);
  const data = await response.json();
  return data;
}
```

### cURL 示例

```bash
# 获取用户档案
curl -X GET "http://localhost:7001/api/user/profile" \
  -H "Cookie: mindelixir=your_session_cookie"

# 更新用户档案
curl -X PUT "http://localhost:7001/api/user/profile" \
  -H "Content-Type: application/json" \
  -H "Cookie: mindelixir=your_session_cookie" \
  -d '{
    "displayName": "张三",
    "bio": "我是一个开发者",
    "location": "北京",
    "socialLinks": [
      {
        "platform": "github",
        "url": "https://github.com/username"
      }
    ],
    "isPublic": true
  }'

# 查询公开用户档案
curl -X GET "http://localhost:7001/api/public/profile?loginId=12345&provider=github"
```

## 注意事项

1. **数据分离**: 用户数据和档案信息分别存储在不同的collection中
2. **公开性**: 档案信息是公开的，任何人都可以通过loginId和provider查询
3. **隐私控制**: 通过`isPublic`字段控制档案是否对外公开
4. **字段验证**: 社交链接的URL必须是有效格式，自定义链接必须提供label
5. **更新机制**: 更新操作会完全替换现有档案信息，支持upsert操作
6. **认证要求**: 获取和更新个人档案需要OAuth登录，查询公开档案无需认证
7. **唯一标识**: 使用loginId和provider的组合作为档案的唯一标识
